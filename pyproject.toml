[project]
name = "Flask"
description = "A simple framework for building complex web applications."
readme = "README.rst"
license = {text = "BSD-3-Clause"}
maintainers = [{name = "Palle<PERSON>", email = "<EMAIL>"}]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Environment :: Web Environment",
    "Framework :: Flask",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: BSD License",
    "Operating System :: OS Independent",
    "Programming Language :: Python",
    "Topic :: Internet :: WWW/HTTP :: Dynamic Content",
    "Topic :: Internet :: WWW/HTTP :: WSGI",
    "Topic :: Internet :: WWW/HTTP :: WSGI :: Application",
    "Topic :: Software Development :: Libraries :: Application Frameworks",
]
requires-python = ">=3.8"
dependencies = [
    "Werkzeug>=2.3.6",
    "Jinja2>=3.1.2",
    "itsdangerous>=2.1.2",
    "click>=8.1.3",
    "blinker>=1.6.2",
    "importlib-metadata>=3.6.0; python_version < '3.10'",
]
dynamic = ["version"]

[project.urls]
Donate = "https://palletsprojects.com/donate"
Documentation = "https://flask.palletsprojects.com/"
Changes = "https://flask.palletsprojects.com/changes/"
"Source Code" = "https://github.com/pallets/flask/"
"Issue Tracker" = "https://github.com/pallets/flask/issues/"
Chat = "https://discord.gg/pallets"

[project.optional-dependencies]
async = ["asgiref>=3.2"]
dotenv = ["python-dotenv"]

[project.scripts]
flask = "flask.cli:main"

[build-system]
requires = ["setuptools"]
build-backend = "setuptools.build_meta"

[tool.setuptools.dynamic]
version = {attr = "flask.__version__"}

[tool.pytest.ini_options]
testpaths = ["tests"]
filterwarnings = [
    "error",
    # change in Python 3.12 causes warning from inside pytest
    "ignore:ast:DeprecationWarning",
]

[tool.coverage.run]
branch = true
source = ["flask", "tests"]

[tool.coverage.paths]
source = ["src", "*/site-packages"]

[tool.mypy]
python_version = "3.8"
files = ["src/flask"]
show_error_codes = true
pretty = true
#strict = true
allow_redefinition = true
disallow_subclassing_any = true
#disallow_untyped_calls = true
#disallow_untyped_defs = true
#disallow_incomplete_defs = true
no_implicit_optional = true
local_partial_types = true
#no_implicit_reexport = true
strict_equality = true
warn_redundant_casts = true
warn_unused_configs = true
warn_unused_ignores = true
#warn_return_any = true
#warn_unreachable = true

[[tool.mypy.overrides]]
module = [
    "asgiref.*",
    "dotenv.*",
    "cryptography.*",
    "importlib_metadata",
]
ignore_missing_imports = true
