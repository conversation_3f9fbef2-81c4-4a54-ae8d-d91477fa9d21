[project]
name = "js_example"
version = "1.1.0"
description = "Demonstrates making AJAX requests to Flask."
readme = "README.rst"
license = {text = "BSD-3-Clause"}
maintainers = [{name = "<PERSON><PERSON><PERSON>", email = "<EMAIL>"}]
dependencies = ["flask"]

[project.urls]
Documentation = "https://flask.palletsprojects.com/patterns/jquery/"

[project.optional-dependencies]
test = ["pytest"]

[build-system]
requires = ["setuptools"]
build-backend = "setuptools.build_meta"

[tool.pytest.ini_options]
testpaths = ["tests"]
filterwarnings = ["error"]

[tool.coverage.run]
branch = true
source = ["js_example", "tests"]
