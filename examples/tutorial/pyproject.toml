[project]
name = "flaskr"
version = "1.0.0"
description = "The basic blog app built in the Flask tutorial."
readme = "README.rst"
license = {text = "BSD-3-Clause"}
maintainers = [{name = "<PERSON><PERSON><PERSON>", email = "<EMAIL>"}]
dependencies = [
    "flask",
]

[project.urls]
Documentation = "https://flask.palletsprojects.com/tutorial/"

[project.optional-dependencies]
test = ["pytest"]

[build-system]
requires = ["setuptools"]
build-backend = "setuptools.build_meta"

[tool.pytest.ini_options]
testpaths = ["tests"]
filterwarnings = ["error"]

[tool.coverage.run]
branch = true
source = ["flaskr", "tests"]
