#
# This file is autogenerated by pip-compile with Python 3.11
# by the following command:
#
#    pip-compile --resolver=backtracking pyproject.toml
#
amqp==5.1.1
    # via kombu
async-timeout==4.0.2
    # via redis
billiard==*******
    # via celery
blinker==1.6.2
    # via flask
celery[redis]==5.2.7
    # via flask-example-celery (pyproject.toml)
click==8.1.3
    # via
    #   celery
    #   click-didyoumean
    #   click-plugins
    #   click-repl
    #   flask
click-didyoumean==0.3.0
    # via celery
click-plugins==1.1.1
    # via celery
click-repl==0.2.0
    # via celery
flask==2.3.2
    # via flask-example-celery (pyproject.toml)
itsdangerous==2.1.2
    # via flask
jinja2==3.1.2
    # via flask
kombu==5.2.4
    # via celery
markupsafe==2.1.2
    # via
    #   jinja2
    #   werkzeug
prompt-toolkit==3.0.38
    # via click-repl
pytz==2023.3
    # via celery
redis==4.5.4
    # via celery
six==1.16.0
    # via click-repl
vine==5.0.0
    # via
    #   amqp
    #   celery
    #   kombu
wcwidth==0.2.6
    # via prompt-toolkit
werkzeug==2.3.3
    # via flask
