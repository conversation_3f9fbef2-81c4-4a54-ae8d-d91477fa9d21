ci:
  autoupdate_branch: "2.3.x"
  autoupdate_schedule: monthly
repos:
  - repo: https://github.com/asottile/pyupgrade
    rev: v3.4.0
    hooks:
      - id: pyupgrade
        args: ["--py38-plus"]
  - repo: https://github.com/asottile/reorder-python-imports
    rev: v3.9.0
    hooks:
      - id: reorder-python-imports
        name: Reorder Python imports (src, tests)
        files: "^(?!examples/)"
        args: ["--application-directories", "src"]
  - repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
      - id: black
  - repo: https://github.com/PyCQA/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        additional_dependencies:
          - flake8-bugbear
          - flake8-implicit-str-concat
  - repo: https://github.com/peterdemin/pip-compile-multi
    rev: v2.6.3
    hooks:
      - id: pip-compile-multi-verify
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: fix-byte-order-marker
      - id: trailing-whitespace
      - id: end-of-file-fixer
