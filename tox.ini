[tox]
envlist =
    py3{12,11,10,9,8}
    pypy39
    py311-min
    py38-dev
    style
    typing
    docs
skip_missing_interpreters = true

[testenv]
package = wheel
wheel_build_env = .pkg
envtmpdir = {toxworkdir}/tmp/{envname}
constrain_package_deps = true
use_frozen_constraints = true
deps =
    -r requirements/tests.txt
    min: -r requirements/tests-pallets-min.txt
    dev: https://github.com/pallets/werkzeug/archive/refs/heads/main.tar.gz
    dev: https://github.com/pallets/jinja/archive/refs/heads/main.tar.gz
    dev: https://github.com/pallets/markupsafe/archive/refs/heads/main.tar.gz
    dev: https://github.com/pallets/itsdangerous/archive/refs/heads/main.tar.gz
    dev: https://github.com/pallets/click/archive/refs/heads/main.tar.gz
#    examples/tutorial[test]
#    examples/javascript[test]
# commands = pytest -v --tb=short --basetemp={envtmpdir} {posargs:tests examples}
commands = pytest -v --tb=short --basetemp={envtmpdir} {posargs:tests}

[testenv:style]
deps = pre-commit
skip_install = true
commands = pre-commit run --all-files

[testenv:typing]
package = wheel
wheel_build_env = .pkg
constrain_package_deps = true
use_frozen_constraints = true
deps = -r requirements/typing.txt
commands = mypy

[testenv:docs]
package = wheel
wheel_build_env = .pkg
constrain_package_deps = true
use_frozen_constraints = true
deps = -r requirements/docs.txt
commands = sphinx-build -W -b html -d {envtmpdir}/doctrees docs {envtmpdir}/html
