name: Tests
on:
  push:
    branches:
      - main
      - '*.x'
    paths-ignore:
      - 'docs/**'
      - '*.md'
      - '*.rst'
  pull_request:
    branches:
      - main
      - '*.x'
    paths-ignore:
      - 'docs/**'
      - '*.md'
      - '*.rst'
jobs:
  tests:
    name: ${{ matrix.name }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        include:
          - {name: Linux, python: '3.11', os: ubuntu-latest, tox: py311}
          - {name: Windows, python: '3.11', os: windows-latest, tox: py311}
          - {name: Mac, python: '3.11', os: macos-latest, tox: py311}
          - {name: '3.12-dev', python: '3.12-dev', os: ubuntu-latest, tox: py312}
          - {name: '3.10', python: '3.10', os: ubuntu-latest, tox: py310}
          - {name: '3.9', python: '3.9', os: ubuntu-latest, tox: py39}
          - {name: '3.8', python: '3.8', os: ubuntu-latest, tox: py38}
          - {name: 'PyPy', python: 'pypy-3.9', os: ubuntu-latest, tox: pypy39}
          - {name: 'Minimum Versions', python: '3.11', os: ubuntu-latest, tox: py311-min}
          - {name: 'Development Versions', python: '3.8', os: ubuntu-latest, tox: py38-dev}
          - {name: Typing, python: '3.11', os: ubuntu-latest, tox: typing}
    steps:
      - uses: actions/checkout@8e5e7e5ab8b370d6c329ec480221332ada57f0ab
      - uses: actions/setup-python@bd6b4b6205c4dbad673328db7b31b7fab9e241c0
        with:
          python-version: ${{ matrix.python }}
          cache: 'pip'
          cache-dependency-path: 'requirements/*.txt'
      - name: cache mypy
        uses: actions/cache@88522ab9f39a2ea568f7027eddc7d8d8bc9d59c8
        with:
          path: ./.mypy_cache
          key: mypy|${{ matrix.python }}|${{ hashFiles('pyproject.toml') }}
        if: matrix.tox == 'typing'
      - run: pip install tox
      - run: tox run -e ${{ matrix.tox }}
