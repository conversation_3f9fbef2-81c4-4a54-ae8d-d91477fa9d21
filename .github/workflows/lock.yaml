name: 'Lock threads'
# Lock closed issues that have not received any further activity for
# two weeks. This does not close open issues, only humans may do that.
# We find that it is easier to respond to new issues with fresh examples
# rather than continuing discussions on old issues.

on:
  schedule:
    - cron: '0 0 * * *'

permissions:
  issues: write
  pull-requests: write

concurrency:
  group: lock

jobs:
  lock:
    runs-on: ubuntu-latest
    steps:
      - uses: dessant/lock-threads@c1b35aecc5cdb1a34539d14196df55838bb2f836
        with:
          issue-inactive-days: 14
          pr-inactive-days: 14
