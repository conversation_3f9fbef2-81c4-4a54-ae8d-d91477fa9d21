# Security Policy

If you believe you have identified a security issue with a <PERSON>lle<PERSON>
project, **do not open a public issue**. To responsibly report a
security issue, <NAME_EMAIL>. A security
team member will contact you acknowledging the report and how to
continue.

Be sure to include as much detail as necessary in your report. As with
reporting normal issues, a minimal reproducible example will help the
maintainers address the issue faster. If you are able, you may also
include a fix for the issue generated with `git format-patch`.

The current and previous release will receive security patches, with
older versions evaluated based on usage information and severity.

After fixing an issue, we will make a security release along with an
announcement on our blog. We may obtain a CVE id as well. You may
include a name and link if you would like to be credited for the report.
