# SHA1:34fd4ca6516e97c7348e6facdd9c4ebb68209d1c
#
# This file is autogenerated by pip-compile-multi
# To update, run:
#
#    pip-compile-multi
#
alabaster==0.7.13
    # via sphinx
babel==2.12.1
    # via sphinx
certifi==2023.5.7
    # via requests
charset-normalizer==3.1.0
    # via requests
docutils==0.18.1
    # via
    #   sphinx
    #   sphinx-tabs
idna==3.4
    # via requests
imagesize==1.4.1
    # via sphinx
jinja2==3.1.2
    # via sphinx
markupsafe==2.1.3
    # via jinja2
packaging==23.1
    # via
    #   pallets-sphinx-themes
    #   sphinx
pallets-sphinx-themes==2.1.1
    # via -r requirements/docs.in
pygments==2.15.1
    # via
    #   sphinx
    #   sphinx-tabs
requests==2.31.0
    # via sphinx
snowballstemmer==2.2.0
    # via sphinx
sphinx==7.0.1
    # via
    #   -r requirements/docs.in
    #   pallets-sphinx-themes
    #   sphinx-issues
    #   sphinx-tabs
    #   sphinxcontrib-log-cabinet
sphinx-issues==3.0.1
    # via -r requirements/docs.in
sphinx-tabs==3.4.1
    # via -r requirements/docs.in
sphinxcontrib-applehelp==1.0.4
    # via sphinx
sphinxcontrib-devhelp==1.0.2
    # via sphinx
sphinxcontrib-htmlhelp==2.0.1
    # via sphinx
sphinxcontrib-jsmath==1.0.1
    # via sphinx
sphinxcontrib-log-cabinet==1.0.1
    # via -r requirements/docs.in
sphinxcontrib-qthelp==1.0.3
    # via sphinx
sphinxcontrib-serializinghtml==1.1.5
    # via sphinx
urllib3==2.0.3
    # via requests
