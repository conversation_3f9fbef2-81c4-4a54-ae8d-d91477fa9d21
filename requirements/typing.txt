# SHA1:7cc3f64d4e78db89d81680ac81503d5ac35d31a9
#
# This file is autogenerated by pip-compile-multi
# To update, run:
#
#    pip-compile-multi
#
cffi==1.15.1
    # via cryptography
cryptography==41.0.0
    # via -r requirements/typing.in
mypy==1.3.0
    # via -r requirements/typing.in
mypy-extensions==1.0.0
    # via mypy
pycparser==2.21
    # via cffi
types-contextvars==*******
    # via -r requirements/typing.in
types-dataclasses==0.6.6
    # via -r requirements/typing.in
types-setuptools==********
    # via -r requirements/typing.in
typing-extensions==4.6.2
    # via mypy
