# SHA1:54b5b77ec8c7a0064ffa93b2fd16cb0130ba177c
#
# This file is autogenerated by pip-compile-multi
# To update, run:
#
#    pip-compile-multi
#
-r docs.txt
-r tests.txt
-r typing.txt
build==0.10.0
    # via pip-tools
cachetools==5.3.1
    # via tox
cfgv==3.3.1
    # via pre-commit
chardet==5.1.0
    # via tox
click==8.1.3
    # via
    #   pip-compile-multi
    #   pip-tools
colorama==0.4.6
    # via tox
distlib==0.3.6
    # via virtualenv
filelock==3.12.0
    # via
    #   tox
    #   virtualenv
identify==2.5.24
    # via pre-commit
nodeenv==1.8.0
    # via pre-commit
pip-compile-multi==2.6.3
    # via -r requirements/dev.in
pip-tools==6.13.0
    # via pip-compile-multi
platformdirs==3.5.1
    # via
    #   tox
    #   virtualenv
pre-commit==3.3.2
    # via -r requirements/dev.in
pyproject-api==1.5.1
    # via tox
pyproject-hooks==1.0.0
    # via build
pyyaml==6.0
    # via pre-commit
toposort==1.10
    # via pip-compile-multi
tox==4.5.2
    # via -r requirements/dev.in
virtualenv==20.23.0
    # via
    #   pre-commit
    #   tox
wheel==0.40.0
    # via pip-tools

# The following packages are considered to be unsafe in a requirements file:
# pip
# setuptools
